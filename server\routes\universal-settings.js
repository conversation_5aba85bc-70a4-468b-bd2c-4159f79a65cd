/**
 * Universal Settings Routes
 * Handles GET and PUT requests for bot universal settings
 */

const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');

// In-memory storage for universal settings (replace with database in production)
// Structure: { userId: { instanceId: { riskLevel, autoRebalance, dcaEnabled } } }
const universalSettingsStore = new Map();

/**
 * GET /api/universal-settings/:instanceId
 * Fetch universal settings for a specific bot instance
 */
router.get('/:instanceId', auth, (req, res) => {
  try {
    const { instanceId } = req.params;
    const userId = req.user.id;

    console.log(`🔧 GET universal settings - User: ${userId}, Bot: ${instanceId}`);

    // Get user's settings
    const userSettings = universalSettingsStore.get(userId) || {};
    const botSettings = userSettings[instanceId];

    if (botSettings) {
      console.log(`✅ Found settings for ${instanceId}:`, botSettings);
      res.json(botSettings);
    } else {
      // Return default settings if none exist
      const defaultSettings = {
        riskLevel: 50,
        autoRebalance: true,
        dcaEnabled: true
      };
      console.log(`⚠️ No settings found for ${instanceId}, returning defaults:`, defaultSettings);
      res.json(defaultSettings);
    }
  } catch (error) {
    console.error('❌ Error fetching universal settings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch universal settings' 
    });
  }
});

/**
 * PUT /api/universal-settings/:instanceId
 * Update universal settings for a specific bot instance
 */
router.put('/:instanceId', auth, (req, res) => {
  try {
    const { instanceId } = req.params;
    const userId = req.user.id;
    const { riskLevel, autoRebalance, dcaEnabled } = req.body;

    console.log(`🔧 PUT universal settings - User: ${userId}, Bot: ${instanceId}`, {
      riskLevel,
      autoRebalance,
      dcaEnabled
    });

    // Validate input
    if (typeof riskLevel !== 'number' || riskLevel < 0 || riskLevel > 100) {
      return res.status(400).json({ 
        success: false, 
        message: 'Risk level must be a number between 0 and 100' 
      });
    }

    if (typeof autoRebalance !== 'boolean') {
      return res.status(400).json({ 
        success: false, 
        message: 'Auto-rebalance must be a boolean value' 
      });
    }

    if (typeof dcaEnabled !== 'boolean') {
      return res.status(400).json({ 
        success: false, 
        message: 'DCA enabled must be a boolean value' 
      });
    }

    // Get or create user settings object
    let userSettings = universalSettingsStore.get(userId);
    if (!userSettings) {
      userSettings = {};
      universalSettingsStore.set(userId, userSettings);
    }

    // Update bot settings
    const settings = {
      riskLevel,
      autoRebalance,
      dcaEnabled,
      updatedAt: new Date().toISOString()
    };

    userSettings[instanceId] = settings;

    console.log(`✅ Updated settings for ${instanceId}:`, settings);
    
    res.json({
      success: true,
      data: settings,
      message: 'Universal settings updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating universal settings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update universal settings' 
    });
  }
});

/**
 * GET /api/universal-settings
 * Get all universal settings for the authenticated user (optional endpoint)
 */
router.get('/', auth, (req, res) => {
  try {
    const userId = req.user.id;
    
    const userSettings = universalSettingsStore.get(userId) || {};
    
    console.log(`📋 GET all universal settings for user ${userId}:`, userSettings);
    
    res.json({
      success: true,
      data: userSettings
    });
  } catch (error) {
    console.error('❌ Error fetching all universal settings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch universal settings' 
    });
  }
});

module.exports = router;