/**
 * Universal Settings API Service
 * Handles GET and PUT requests for bot universal settings
 * Uses FreqTrade API with localStorage fallback
 */

import { getAuthToken } from './api';

export interface UniversalSettings {
  riskLevel: number;        // Risk Level slider (0-100%)
  autoRebalance: boolean;   // Auto-Rebalance toggle (On/Off)
  dcaEnabled: boolean;      // DCA Enabled toggle (On/Off)
}

export interface UniversalSettingsResponse {
  success: boolean;
  data?: UniversalSettings;
  message?: string;
}

const API_BASE = 'https://freqtrade.crypto-pilot.dev/api';
const STORAGE_KEY = 'freqtrade_universal_settings';

/**
 * Get user ID for storage key
 */
const getUserStorageKey = (): string => {
  try {
    const token = getAuthToken();
    if (!token) return 'anonymous';
    
    // Extract user info from token if possible, otherwise use generic key
    // This is a simple approach - in production you'd decode the JWT properly
    return `user_${token.substring(0, 8)}`;
  } catch {
    return 'anonymous';
  }
};

/**
 * Store settings in localStorage
 */
const storeSettingsLocally = (instanceId: string, settings: UniversalSettings): void => {
  try {
    const userKey = getUserStorageKey();
    const storageKey = `${STORAGE_KEY}_${userKey}`;
    const existingData = JSON.parse(localStorage.getItem(storageKey) || '{}');
    
    existingData[instanceId] = {
      ...settings,
      updatedAt: new Date().toISOString()
    };
    
    localStorage.setItem(storageKey, JSON.stringify(existingData));
    console.log('🔧 Settings stored locally for:', instanceId);
  } catch (error) {
    console.error('❌ Failed to store settings locally:', error);
  }
};

/**
 * Retrieve settings from localStorage
 */
const getSettingsLocally = (instanceId: string): UniversalSettings | null => {
  try {
    const userKey = getUserStorageKey();
    const storageKey = `${STORAGE_KEY}_${userKey}`;
    const existingData = JSON.parse(localStorage.getItem(storageKey) || '{}');
    
    const settings = existingData[instanceId];
    if (settings) {
      console.log('🔧 Settings retrieved locally for:', instanceId);
      return {
        riskLevel: settings.riskLevel,
        autoRebalance: settings.autoRebalance,
        dcaEnabled: settings.dcaEnabled
      };
    }
    
    return null;
  } catch (error) {
    console.error('❌ Failed to retrieve settings locally:', error);
    return null;
  }
};

/**
 * Fetch universal settings for a specific bot instance
 */
export const getUniversalSettings = async (instanceId: string): Promise<UniversalSettingsResponse> => {
  try {
    console.log('🔧 Fetching universal settings for:', instanceId);
    
    // Try to fetch from FreqTrade API first
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_BASE}/universal-settings/${instanceId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Settings fetched from FreqTrade API:', data);
        return {
          success: true,
          data: data
        };
      } else {
        console.warn('⚠️ FreqTrade API returned:', response.status);
        throw new Error(`API returned ${response.status}`);
      }
    } catch (apiError) {
      console.warn('⚠️ FreqTrade API unavailable, using localStorage fallback:', apiError);
      
      // Fallback to localStorage
      const localSettings = getSettingsLocally(instanceId);
      if (localSettings) {
        return {
          success: true,
          data: localSettings
        };
      }
      
      // Return default settings if nothing found
      console.log('🔧 Using default settings for:', instanceId);
      const defaultSettings: UniversalSettings = {
        riskLevel: 50,
        autoRebalance: true,
        dcaEnabled: true
      };
      
      return {
        success: true,
        data: defaultSettings
      };
    }
  } catch (error) {
    console.error('❌ Failed to fetch universal settings:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to fetch settings'
    };
  }
};

/**
 * Update universal settings for a specific bot instance
 */
export const updateUniversalSettings = async (
  instanceId: string, 
  settings: UniversalSettings
): Promise<UniversalSettingsResponse> => {
  try {
    console.log('🔧 Updating universal settings for:', instanceId, settings);
    
    // Try to save to FreqTrade API first
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_BASE}/universal-settings/${instanceId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Settings saved to FreqTrade API:', data);
        
        // Also store locally as backup
        storeSettingsLocally(instanceId, settings);
        
        return {
          success: true,
          data: data
        };
      } else {
        console.warn('⚠️ FreqTrade API save failed:', response.status);
        throw new Error(`API returned ${response.status}`);
      }
    } catch (apiError) {
      console.warn('⚠️ FreqTrade API unavailable, saving to localStorage:', apiError);
      
      // Fallback to localStorage
      storeSettingsLocally(instanceId, settings);
      
      return {
        success: true,
        data: {
          ...settings,
          updatedAt: new Date().toISOString()
        } as any
      };
    }
  } catch (error) {
    console.error('❌ Failed to update universal settings:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update settings'
    };
  }
};