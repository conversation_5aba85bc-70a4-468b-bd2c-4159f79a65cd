/**
 * FreqTrade SSE Integration Hook
 * Replaces WebSocket-based integration with Server-Sent Events
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { freqTradeSSEService, PortfolioData, BotData, ChartResponse, TradeData, ChartDataPoint } from '@/lib/freqtrade-sse-service';
import { useAuth } from '@/contexts/AuthContext';

interface FreqTradeSSEState {
  // Connection state
  isConnected: boolean;
  connectionError: string | null;
  lastUpdate: Date | null;

  // Portfolio data
  portfolioData: PortfolioData | null;
  portfolioLoading: boolean;
  portfolioError: string | null;

  // Bot data
  bots: BotData[];
  botsLoading: boolean;
  botsError: string | null;

  // Trade data
  trades: TradeData[];
  tradesLoading: boolean;
  tradesError: string | null;

  // Chart data by interval
  chartData: { [interval: string]: ChartResponse };
  chartLoading: boolean;
  chartError: string | null;

  // Actions
  refreshData: () => Promise<void>;
  fetchChartData: (interval: '1h' | '24h' | '7d' | '30d') => Promise<void>;
  fetchAllChartData: () => Promise<void>;
}

export const useFreqTradeSSE = (): FreqTradeSSEState => {
  // Connection state
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // React Strict Mode guard to prevent double initialization
  const initializedRef = useRef(false);

  // Portfolio state
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [portfolioLoading, setPortfolioLoading] = useState(true);
  const [portfolioError, setPortfolioError] = useState<string | null>(null);

  // Bot state
  const [bots, setBots] = useState<BotData[]>([]);
  const [botsLoading, setBotsLoading] = useState(true);
  const [botsError, setBotsError] = useState<string | null>(null);

  // Trade state
  const [trades, setTrades] = useState<TradeData[]>([]);
  const [tradesLoading, setTradesLoading] = useState(true);
  const [tradesError, setTradesError] = useState<string | null>(null);

  // Chart data state
  const [chartData, setChartData] = useState<{ [interval: string]: ChartResponse }>({});
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);

  const { user } = useAuth();
  const unsubscribersRef = useRef<Array<() => void>>([]);

  // Initialize SSE connection when user is authenticated
  useEffect(() => {
    if (!user) return;

    // Prevent double initialization in React Strict Mode (development)
    if (initializedRef.current) {
      return;
    }
    initializedRef.current = true;

    const initializeSSE = async () => {
      try {
        setConnectionError(null);
        await freqTradeSSEService.connect();
      } catch (error) {
        console.error('❌ Failed to initialize SSE connection:', error);
        setConnectionError(error instanceof Error ? error.message : 'Connection failed');
        setPortfolioLoading(false);
        setBotsLoading(false);
      }
    };

    initializeSSE();

    // Setup event listeners
    const unsubscribeConnected = freqTradeSSEService.on('connected', (connected: boolean) => {
      setIsConnected(connected);

      if (connected) {
        setConnectionError(null);
        loadInitialData();
      } else {
        setConnectionError('Disconnected from FreqTrade service');
      }
    });

    const unsubscribePortfolio = freqTradeSSEService.on('portfolio_update', (data: PortfolioData) => {
      setPortfolioData(data);
      setPortfolioLoading(false);
      setPortfolioError(null);
      setLastUpdate(new Date(data.timestamp));

      // Create chart data point for time bucketing
      const liveChartPoint = {
        timestamp: data.timestamp,
        portfolioValue: data.portfolioValue,
        totalPnL: data.totalPnL,
        activeBots: data.activeBots,
        botCount: data.botCount,
      };

      // Time-based aggregation with always-live latest point
      setChartData(prev => {
        const updated = { ...prev };
        const currentTime = new Date(data.timestamp);

        // Define aggregation intervals and time windows
        const intervals = {
          '1h': { bucketMinutes: 5, totalMinutes: 60, maxPoints: 12 },     // 5min intervals for 1 hour
          '24h': { bucketMinutes: 15, totalMinutes: 720, maxPoints: 48 }, // TEMP: 15min intervals for 12 hours (testing for more data)
          '7d': { bucketMinutes: 720, totalMinutes: 10080, maxPoints: 14 }, // 12hr intervals for 7 days
          '30d': { bucketMinutes: 1440, totalMinutes: 43200, maxPoints: 30 } // 24hr intervals for 30 days
        };

        (['1h', '24h', '7d', '30d'] as const).forEach(interval => {
          const config = intervals[interval];
          const bucketMs = config.bucketMinutes * 60 * 1000;
          const totalMs = config.totalMinutes * 60 * 1000;

          // Create the live point (always at current timestamp, not bucketed)
          const livePoint = {
            ...liveChartPoint,
            timestamp: currentTime.toISOString(),
            isLive: true // Mark as live point for identification
          };

          // Initialize if no data exists
          if (!updated[interval] || !updated[interval].data) {
            updated[interval] = {
              success: true,
              interval,
              data: [livePoint], // Start with just the live point
              metadata: {
                totalPoints: 1,
                timeRange: { start: currentTime.toISOString(), end: currentTime.toISOString() },
                aggregationWindow: `${config.bucketMinutes}m`
              }
            };
            return;
          }

          const existingData = [...updated[interval].data];

          // Remove any existing live points (there should only be one at the end)
          const historicalData = existingData.filter(point => (point as any).isLive !== true);

          // Check if we need to create a new historical bucket from the previous live point
          const lastBucketTime = Math.floor((currentTime.getTime() - config.bucketMinutes * 60 * 1000) / bucketMs) * bucketMs;
          const lastBucketTimeStr = new Date(lastBucketTime).toISOString();

          // If enough time has passed since the last bucket, create a historical point
          if (historicalData.length === 0 ||
            new Date(historicalData[historicalData.length - 1].timestamp).getTime() < lastBucketTime) {

            // Add the previous state as a historical bucket point
            const historicalPoint = {
              ...liveChartPoint,
              timestamp: lastBucketTimeStr,
              isLive: false
            };

            historicalData.push(historicalPoint);
          }

          // Sort historical data by timestamp
          historicalData.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          // Remove historical data points older than the time window
          const cutoffTime = currentTime.getTime() - totalMs;
          const filteredHistoricalData = historicalData.filter(point =>
            new Date(point.timestamp).getTime() >= cutoffTime
          );

          // Limit historical points to prevent memory issues (save 1 spot for live point)
          while (filteredHistoricalData.length > config.maxPoints) {
            filteredHistoricalData.shift(); // Remove oldest historical point
          }

          // Combine historical data with the live point at the end
          const finalData = [...filteredHistoricalData, livePoint];

          updated[interval].data = finalData;

          // Update metadata
          const allTimes = finalData.map(p => new Date(p.timestamp).getTime());
          updated[interval].metadata = {
            totalPoints: finalData.length,
            timeRange: {
              start: new Date(Math.min(...allTimes)).toISOString(),
              end: new Date(Math.max(...allTimes)).toISOString()
            },
            aggregationWindow: `${config.bucketMinutes}m`
          };
        }); return updated;
      });
    });

    const unsubscribeBots = freqTradeSSEService.on('bot_update', (botData: BotData[]) => {
      setBots(botData);
      setBotsLoading(false);
      setBotsError(null);
    });

    const unsubscribeLastUpdate = freqTradeSSEService.on('last_update', (date: Date) => {
      setLastUpdate(date);
    });

    const unsubscribeError = freqTradeSSEService.on('error', (error: any) => {
      console.error('❌ FreqTrade SSE error:', error);
      const errorMessage = error?.message || 'SSE connection error';
      setConnectionError(errorMessage);
    });

    const unsubscribeConnectionFailed = freqTradeSSEService.on('connection_failed', (message: string) => {
      console.error('❌ FreqTrade SSE connection failed:', message);
      setConnectionError(message);
      setPortfolioLoading(false);
      setBotsLoading(false);
    });

    // Store unsubscribers
    unsubscribersRef.current = [
      unsubscribeConnected,
      unsubscribePortfolio,
      unsubscribeBots,
      unsubscribeLastUpdate,
      unsubscribeError,
      unsubscribeConnectionFailed,
    ];

    // Cleanup on unmount
    return () => {
      unsubscribersRef.current.forEach(unsubscribe => unsubscribe());
      unsubscribersRef.current = [];
      initializedRef.current = false; // Reset guard for potential remount
      // Add a small delay before disconnecting to avoid rapid reconnections
      setTimeout(() => {
        freqTradeSSEService.disconnect();
      }, 100);
    };
  }, [user]);

  // Load initial data (chart data since portfolio comes via SSE)
  const loadInitialData = async () => {
    try {
      // Fetch initial chart data for all intervals
      await fetchAllChartData();

      // Set initial portfolio loading to false since we have chart data
      // Portfolio data will come via SSE if available
      setPortfolioLoading(false);

      // Try to fetch bot list if available
      try {
        const botList = await freqTradeSSEService.fetchBots();
        if (Array.isArray(botList) && botList.length > 0) {
          setBotsLoading(false);
        }
      } catch (error) {
        setBotsLoading(false);
      }

      // Try to fetch recent trades if available
      try {
        const tradeList = await freqTradeSSEService.fetchTrades();
        if (Array.isArray(tradeList)) {
          setTrades(tradeList);
          setTradesLoading(false);
          setTradesError(null);
        }
      } catch (error) {
        setTradesLoading(false);
        setTradesError(error instanceof Error ? error.message : 'Failed to load trades');
      }

    } catch (error) {
      console.error('❌ Failed to load initial data:', error);
    }
  };

  // Fetch chart data for specific interval
  const fetchChartData = useCallback(async (interval: '1h' | '24h' | '7d' | '30d') => {
    try {
      setChartLoading(true);
      setChartError(null);

      const data = await freqTradeSSEService.fetchChartData(interval);

      setChartData(prev => ({
        ...prev,
        [interval]: data
      }));

    } catch (error) {
      console.error(`❌ Failed to fetch chart data for ${interval}:`, error);
      const errorMessage = error instanceof Error ? error.message : `Failed to load ${interval} data`;
      setChartError(errorMessage);
    } finally {
      setChartLoading(false);
    }
  }, []);

  // Helper function to bucket historical data into time intervals (excluding live point space)
  const bucketHistoricalData = useCallback((rawData: ChartDataPoint[], config: { bucketMinutes: number; totalMinutes: number; maxPoints: number }) => {
    if (!rawData || rawData.length === 0) {
      console.log('🪣 USER-DEBUG: No raw data to bucket');
      return [];
    }

    console.log(`🪣 USER-DEBUG: Starting bucketing process:`, {
      rawDataPoints: rawData.length,
      bucketMinutes: config.bucketMinutes,
      totalMinutes: config.totalMinutes,
      maxPoints: config.maxPoints,
      firstPoint: rawData[0]?.timestamp,
      lastPoint: rawData[rawData.length - 1]?.timestamp
    });

    // Extra debugging for 24H timeframe issue
    if (config.bucketMinutes === 30) {
      console.log(`🪣 24H-DEBUG: Raw data analysis:`, {
        totalRawPoints: rawData.length,
        timeSpan: rawData.length > 0 ? {
          start: rawData[0].timestamp,
          end: rawData[rawData.length - 1].timestamp,
          spanHours: (new Date(rawData[rawData.length - 1].timestamp).getTime() - new Date(rawData[0].timestamp).getTime()) / (1000 * 60 * 60)
        } : null,
        samplePoints: rawData.slice(0, 5).map(p => ({ timestamp: p.timestamp, value: p.portfolioValue }))
      });
    }

    const bucketMs = config.bucketMinutes * 60 * 1000;
    const totalMs = config.totalMinutes * 60 * 1000;
    const now = Date.now();
    const cutoffTime = now - totalMs;

    console.log(`🪣 USER-DEBUG: Time filtering:`, {
      now: new Date(now).toISOString(),
      cutoffTime: new Date(cutoffTime).toISOString(),
      totalWindowHours: config.totalMinutes / 60
    });

    // Group data by time buckets - include ALL buckets including current
    const buckets = new Map<string, ChartDataPoint>();

    let pointsInWindow = 0;
    let pointsBeforeCutoff = 0;

    rawData.forEach(point => {
      const pointTime = new Date(point.timestamp).getTime();

      // Skip points outside the time window
      if (pointTime < cutoffTime) {
        pointsBeforeCutoff++;
        return;
      }

      pointsInWindow++;

      // Round down to bucket boundary
      const bucketTime = Math.floor(pointTime / bucketMs) * bucketMs;
      const bucketKey = new Date(bucketTime).toISOString();

      // Use the latest data point in each bucket (keep all buckets including current)
      if (!buckets.has(bucketKey) || pointTime > new Date(buckets.get(bucketKey)!.timestamp).getTime()) {
        buckets.set(bucketKey, {
          ...point,
          timestamp: bucketKey,
          isLive: bucketTime >= Math.floor(now / bucketMs) * bucketMs // Mark current bucket as live
        });
      }
    });

    console.log(`🪣 USER-DEBUG: Filtering results:`, {
      totalRawPoints: rawData.length,
      pointsBeforeCutoff,
      pointsInWindow,
      bucketsCreated: buckets.size
    });

    // Convert to array and sort by time
    const bucketedData = Array.from(buckets.values()).sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    // Limit to max points (save 1 space for live point)
    const originalLength = bucketedData.length;
    while (bucketedData.length > config.maxPoints) {
      bucketedData.shift();
    }

    console.log(`🪣 USER-DEBUG: Final bucketing result:`, {
      originalBuckets: originalLength,
      finalBuckets: bucketedData.length,
      maxAllowed: config.maxPoints,
      truncated: originalLength > config.maxPoints,
      timeSpan: bucketedData.length > 0 ? {
        start: bucketedData[0].timestamp,
        end: bucketedData[bucketedData.length - 1].timestamp
      } : null
    });

    // Extra debugging for 24H specific issue
    if (config.bucketMinutes === 30) {
      console.log(`🪣 24H-DEBUG: Final result analysis:`, {
        expectedPoints: config.maxPoints,
        actualPoints: bucketedData.length,
        isCorrect: bucketedData.length === config.maxPoints,
        issue: bucketedData.length < config.maxPoints ? 'Not enough data in time window' : 'Data trimmed correctly',
        sampleBuckets: bucketedData.slice(0, 3).map(p => ({
          timestamp: p.timestamp,
          value: p.portfolioValue,
          isLive: p.isLive
        }))
      });
    }

    return bucketedData;
  }, []);  // Fetch all chart data intervals
  const fetchAllChartData = useCallback(async () => {
    try {
      setChartLoading(true);
      setChartError(null);

      // Define the same intervals as in live updates for consistency
      const intervalConfigs = {
        '1h': { bucketMinutes: 5, totalMinutes: 60, maxPoints: 12 },     // 5min intervals for 1 hour
        '24h': { bucketMinutes: 15, totalMinutes: 720, maxPoints: 48 }, // TEMP: 15min intervals for 12 hours (testing for more data)
        '7d': { bucketMinutes: 720, totalMinutes: 10080, maxPoints: 14 }, // 12hr intervals for 7 days
        '30d': { bucketMinutes: 1440, totalMinutes: 43200, maxPoints: 30 } // 24hr intervals for 30 days
      };

      // Fetch all data from server
      const serverData = await freqTradeSSEService.fetchAllChartData();

      // Process server data through our time bucketing system
      const processedData: { [key: string]: ChartResponse } = {};

      for (const [interval, config] of Object.entries(intervalConfigs)) {
        const serverInterval = serverData[interval];

        if (serverInterval && serverInterval.data && serverInterval.data.length > 0) {
          // Apply time bucketing to historical data
          const bucketedData = bucketHistoricalData(serverInterval.data, config);

          processedData[interval] = {
            success: true,
            interval,
            data: bucketedData,
            metadata: {
              totalPoints: bucketedData.length,
              timeRange: bucketedData.length > 0 ? {
                start: bucketedData[0].timestamp,
                end: bucketedData[bucketedData.length - 1].timestamp
              } : { start: '', end: '' },
              aggregationWindow: `${config.bucketMinutes}m`
            }
          };

        } else {
          processedData[interval] = {
            success: true,
            interval,
            data: [],
            metadata: {
              totalPoints: 0,
              timeRange: { start: '', end: '' },
              aggregationWindow: `${config.bucketMinutes}m`
            }
          };
        }
      }

      setChartData(processedData);
      setChartError(null);
    } catch (error) {
      console.error('❌ Failed to fetch all chart data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load chart data';
      setChartError(errorMessage);
    } finally {
      setChartLoading(false);
    }
  }, [bucketHistoricalData]);

  // Periodic refresh for chart data to ensure we get latest snapshots
  useEffect(() => {
    if (!isConnected) return;

    const refreshInterval = setInterval(async () => {
      try {
        console.log('📊 CHART: Periodic refresh (5min)');
        await fetchAllChartData();
      } catch (error) {
        console.error('❌ CHART: Periodic refresh failed:', error);
      }
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => {
      clearInterval(refreshInterval);
    };
  }, [isConnected, fetchAllChartData]);

  // Manual refresh function
  const refreshData = useCallback(async () => {
    try {
      // Reconnect if disconnected
      if (!isConnected) {
        await freqTradeSSEService.connect();
      }

      // Refresh chart data
      await fetchAllChartData();

    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
      setConnectionError(error instanceof Error ? error.message : 'Refresh failed');
    }
  }, [isConnected, fetchAllChartData]);

  return {
    // Connection state
    isConnected,
    connectionError,
    lastUpdate,

    // Portfolio data
    portfolioData,
    portfolioLoading,
    portfolioError,

    // Bot data
    bots,
    botsLoading,
    botsError,

    // Trade data
    trades,
    tradesLoading,
    tradesError,

    // Chart data
    chartData,
    chartLoading,
    chartError,

    // Actions
    refreshData,
    fetchChartData,
    fetchAllChartData,
  };
};

export default useFreqTradeSSE;
